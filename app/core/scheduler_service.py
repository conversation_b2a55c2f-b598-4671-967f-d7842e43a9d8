"""
定时任务服务模块
"""
from datetime import datetime, timedelta
import threading
import time
import traceback
from typing import Optional

import pandas as pd
from pytz import timezone
import requests
import schedule

from app.config import PKL_DIR, SYMBOLS
from app.core.fetch_data import (
    crawl_all_data,
    crawl_recent_bar_data,
    make_request_with_rate,
)
from app.core.model_service import model_service
from app.core.prediction_service import prediction_service
from app.crud import bar_data_service
from app.crud.prediction_history_service import prediction_history_service
from app.logger_config import get_module_logger
from app.models.prediction_history import PredictionHistory
import pytz


class SchedulerService:
    """定时任务服务"""

    def __init__(self):
        self.scheduler_thread = None
        self.logger = get_module_logger("scheduler_service")

        # 直接使用数据库

        self.prediction_history_service = prediction_history_service

    def train_and_save_model(
            self, target_symbol: str, date: Optional[datetime] = datetime.now()
    ):
        """训练并保存模型"""
        try:

            # 生成带日期的模型文件名
            model_path = f"{PKL_DIR}/{target_symbol}"

            data = bar_data_service.load_bar_data(target_symbol, end_time=date)

            data['Datetime'] = pd.to_datetime(data['Date'] + ' ' + data['Time'])
            data['datetime'] = data.apply(lambda x: x['Date'] + ' ' + x['Time'], axis=1)
            data['datetime'] = pd.to_datetime(data['datetime'])

            self.logger.info(f"开始训练 {target_symbol} 模型")
            if data.empty:
                raise Exception(f"数据库中没有找到 {target_symbol} 的数据")

            model_service.create_model(target_symbol, data, model_path, date)

            self.logger.info(f"{target_symbol} 模型训练完成: {model_path}")

            return True

        except Exception as e:
            self.logger.error(
                f"{target_symbol if 'target_symbol' in locals() else 'unknown'} 模型训练失败: {e}"
            )
            return False

    def fetch_closing_price(self, symbol=None):
        """获取当天下午4点的收盘价并更新history.json"""
        try:
            target_symbol = symbol
            from datetime import timezone
            dt_midnight = datetime.now(timezone.utc)
            utc_timestamp = timestamp_ms = int(dt_midnight.timestamp() * 1000)
            closing_price = max(make_request_with_rate(symbol)["data"]["bars"], key=lambda x: x["time"])[
                "open"]
            self.logger.info(
                f"获取 {target_symbol} 北京时间 {datetime.now().date()} 下午4点的收盘价，UTC时间戳: {utc_timestamp}, 收盘价：{closing_price}"
            )
            if closing_price is not None:
                record = prediction_history_service.get_record(datetime.now().date().strftime("%Y-%m-%d"),
                                                               target_symbol)
                record["Actual 16:00 Price"] = closing_price
                prediction_history_service.update(record)
                self.logger.info(f"成功更新 {target_symbol} 收盘价: {closing_price}")
                return True
            else:
                self.logger.warning(f"获取 {target_symbol} 收盘价失败")
                return False
        except Exception as e:
            self.logger.error(
                f"获取 {target_symbol if 'target_symbol' in locals() else 'unknown'} 收盘价时发生错误: {traceback.print_exc()}"
            )
            return False

    def _call_api_for_closing_price(self, timestamp, symbol):
        """
        调用API获取收盘价的方法
        参数:
            timestamp: UTC时间戳（毫秒）
            symbol: 交易对符号
        返回:
            float: 收盘价，如果获取失败返回None
        """
        try:
            # 构建API URL，endTime和_参数都用毫秒级时间戳
            self.logger.info(f"调用API获取 {symbol} 收盘价")
            data = crawl_recent_bar_data(symbol, timestamp)
            if data is not None:
                if data.get("success") and data.get("data"):
                    bar_data = data["data"][0]
                    closing_price = bar_data.get("open")
                    if closing_price is not None:
                        self.logger.info(f"成功获取 {symbol} 收盘价: {closing_price}")
                        return float(closing_price)
                    else:
                        self.logger.warning(f"API返回数据中没有找到 {symbol} open价格")
                        return None
                else:
                    self.logger.error(f"API返回 {symbol} 数据格式错误: {data}")
                    return None
            else:
                self.logger.error(
                    f"API调用 {symbol} 失败，状态码"
                )
                return None
        except Exception as e:
            self.logger.error(f"API调用 {symbol} 异常: {e}")
            return None

    def daily_prediction(self):


        self.logger.info(f"开始执行每日定时任务: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"配置的交易对: {SYMBOLS}")

        # 为每个symbol执行完整的预测流程
        for symbol in SYMBOLS:
            self.logger.info(f"\n{'=' * 50}")
            self.logger.info(f"开始处理交易对: {symbol}")
            self.logger.info(f"{'=' * 50}")

            # 1. 获取最近三天的数据
            self.logger.info(f"步骤1: 获取 {symbol} 最近三天的数据...")
            target_timestamp = int((datetime.utcnow() - timedelta(days=3)).timestamp() * 1000)
            results = crawl_recent_bar_data(symbol, target_timestamp)
            if results is not None:
                self.logger.info(f"{symbol} 数据获取成功")
                bar_data_service.save_bar_data(results["bars"], symbol)
                # 2. 训练新模型
                self.logger.info(f"步骤2: 训练 {symbol} 新模型...")
                if self.train_and_save_model(symbol):
                    self.logger.info(f"{symbol} 模型训练成功")

                    # 3. 使用新模型进行预测
                    self.logger.info(f"步骤3: 使用 {symbol} 新模型进行预测...")
                    data = bar_data_service.load_bar_data(symbol, end_time=datetime.now())
                    model_path = f"{PKL_DIR}/{symbol}/{symbol}_1H_SVM_{datetime.now().strftime("%Y%m%d")}.pkl"
                    result = prediction_service.predict(data, model_path, days=1)[-1]
                    if result is not None:
                        self.logger.info(f"{symbol} 预测完成")
                        self.prediction_history_service.create(result)
                    else:
                        self.logger.error(f"{symbol} 预测失败")
                else:
                    self.logger.error(f"{symbol} 模型训练失败")
            else:
                self.logger.error(f"{symbol} 数据获取失败")

        self.logger.info(f"\n{'=' * 50}")
        self.logger.info("所有交易对处理完成")
        self.logger.info(f"{'=' * 50}")

    def fetch_all_closing_prices(self):
        """
        获取所有交易对的收盘价并完整更新价差计算
        基于main.py中update_actual_prices函数的完整逻辑
        """
        self.logger.info(f"开始获取所有交易对的收盘价并更新价差计算...")
        success_count = 0
        total_symbols = len(SYMBOLS)

        for symbol in SYMBOLS:
            self.logger.info(f"\n{'=' * 50}")
            self.logger.info(f"处理交易对: {symbol} ({SYMBOLS.index(symbol) + 1}/{total_symbols})")
            self.logger.info(f"{'=' * 50}")

            try:
                # 1. 获取收盘价并保存
                if self.fetch_closing_price(symbol):
                    self.logger.info(f"✅ {symbol} 收盘价获取成功")

                    # 2. 执行完整的价差计算和胜率统计
                    if self._update_predictions_with_actual_prices(symbol):
                        self.logger.info(f"✅ {symbol} 价差计算和胜率统计完成")
                        success_count += 1
                    else:
                        self.logger.warning(f"⚠️ {symbol} 价差计算失败，但收盘价已更新")
                        success_count += 1  # 收盘价获取成功仍算成功
                else:
                    self.logger.error(f"❌ {symbol} 收盘价获取失败")
            except Exception as e:
                self.logger.error(f"❌ {symbol} 处理异常: {e}")

        self.logger.info(f"\n{'=' * 50}")
        self.logger.info(f"所有交易对收盘价获取完成")
        self.logger.info(f"成功: {success_count}/{total_symbols}")
        self.logger.info(f"{'=' * 60}")
        return success_count > 0

    def _update_predictions_with_actual_prices(self, symbol):
        """
        更新预测结果中的实际价格，计算价差、偏离度和预测胜率
        使用数据库方式

        参数:
            symbol: 交易对符号
        返回:
            bool: 是否成功更新
        """
        try:
            # 1. 获取当天的预测数据
            today = datetime.now().strftime('%Y-%m-%d')
            prediction = prediction_history_service.get_record(today, symbol)
            if not prediction:
                self.logger.warning(f"⚠️ {symbol} 数据库中没有 {today} 的预测记录")
                return False

            self.logger.info(f"📊 {symbol} 从数据库读取到 {today} 的预测记录")

            # 2. 进行数据验证和计算（只处理当天的一条数据）
            return self._calculate_prediction_accuracy_for_db(prediction, symbol)

        except Exception as e:
            self.logger.error(f"❌ {symbol} 更新预测数据时发生错误: {e}")
            return False

    def _calculate_prediction_accuracy_for_db(self, prediction, symbol):
        """
        为数据库版本计算单条预测数据的准确性
        参照_calculate_prediction_accuracy方法的完整逻辑，但只处理当天的一条数据

        参数:
            prediction: 单条预测数据（字典格式）
            symbol: 交易对符号
        返回:
            bool: 是否成功计算
        """
        try:
            self.logger.info(f"🔍 {symbol} 开始计算当天预测准确性...")

            # 1. 数据验证
            if "Date" not in prediction:
                self.logger.warning(f"⚠️ 预测记录缺少Date字段")
                return False

            # 2. 检查是否已有实际价格
            actual_price_value = prediction.get("Actual 16:00 Price")
            if actual_price_value == "N/A" or actual_price_value is None:
                self.logger.warning(f"⚠️ {prediction['Date']} 还没有实际价格，跳过计算")
                return False

            try:
                # 3. 数据类型转换和验证
                actual_close_price = float(actual_price_value)
                predicted_close_price = float(prediction["Predicted 16:00 Price"])
                morning_price = float(prediction["09:00 Price"])

                # 4. 计算预测值和实际值的价差（保持更高精度）
                price_diff = round(actual_close_price - predicted_close_price, 4)

                # 5. 计算偏离度
                if predicted_close_price != 0:
                    deviation = round((price_diff / predicted_close_price) * 100, 4)
                else:
                    deviation = None
                    self.logger.warning(f"⚠️ 预测价格为0，无法计算偏离度")

                # 6. 判断预测方向是否正确
                predicted_direction = prediction.get("Predicted Direction", "")
                actual_direction = (
                    "Up" if actual_close_price > morning_price else "Down"
                )

                # 7. 判断预测是否正确
                is_correct = predicted_direction == actual_direction

                self.logger.info(f"📊 {prediction['Date']} 计算结果:")
                self.logger.info(f"   预测方向: {predicted_direction}")
                self.logger.info(f"   实际方向: {actual_direction}")
                self.logger.info(f"   预测价格: {predicted_close_price}")
                self.logger.info(f"   实际价格: {actual_close_price}")
                self.logger.info(f"   价格差异: {price_diff}")
                self.logger.info(f"   偏离度: {deviation}%")
                self.logger.info(f"   预测结果: {'✅ 正确' if is_correct else '❌ 错误'}")

                # 8. 更新数据库中的计算结果
                success = self._update_single_prediction_result(
                    prediction["Date"], symbol, price_diff, deviation, actual_direction
                )

                if success:
                    self.logger.info(
                        f"✅ {symbol} {prediction['Date']} 预测结果计算完成并已更新到数据库"
                    )
                else:
                    self.logger.warning(f"⚠️ {symbol} {prediction['Date']} 数据库更新失败，但计算完成")

                return True

            except (ValueError, TypeError) as e:
                self.logger.warning(
                    f"⚠️ 数据类型转换失败: {prediction.get('Date', 'Unknown')} - {traceback.print_exc()}")
                return False

        except Exception as e:
            self.logger.error(f"❌ {symbol} 计算预测准确性时发生错误: {e}")
            return False

    def _update_single_prediction_result(
            self, date, symbol, price_diff, deviation, actual_direction
    ):
        """
        更新单条预测记录的计算结果

        参数:
            date: 日期
            symbol: 交易对符号
            price_diff: 价格差异
            deviation: 偏离度
            actual_direction: 实际方向
        返回:
            bool: 是否更新成功
        """
        try:
            # 获取现有记录
            existing_record = self.prediction_history_service.get_record(
                date, symbol
            )
            existing_record = PredictionHistory.from_dict(existing_record)
            if existing_record:
                # 更新计算字段
                existing_record.price_diff_actual_predicted = price_diff
                existing_record.deviation_percentage = deviation
                existing_record.actual_direction = actual_direction
                existing_record.updated_at = datetime.utcnow()

                # 提交更改
                self.prediction_history_service.session.commit()
                self.logger.info(f"✅ 成功更新 {symbol} {date} 的计算结果")
                return True
            else:
                self.logger.warning(f"⚠️ 未找到记录 {symbol} {date}，无法更新")
                return False

        except Exception as e:
            self.logger.error(f"❌ 更新单条预测结果失败: {traceback.print_exc()}")
            # 回滚事务
            try:
                self.prediction_history_service.session.rollback()
            except:
                pass
            return False

    def run_scheduler(self):
        """运行调度器"""
        while True:
            schedule.run_pending()
            time.sleep(60)

    def start_scheduler(self):
        """启动定时任务调度器"""

        # 设置每天指定时间执行任务
        # 使用标记确保每天只执行一次
        last_prediction_run = [None]  # 上次预测任务执行日期
        last_fetch_run = [None]  # 上次获取收盘价任务执行日期

        def run_task():
            """每日定时预测任务 - 支持多个交易对"""
            beijing_tz = timezone("Asia/Shanghai")
            today = datetime.now(beijing_tz).date()
            if today.weekday() > 4:
                self.logger.info(f"今天是周末({today})，不执行每日定时任务。")
                return

            # 获取当前 UTC 时间，并转换为北京时间
            utc_now = datetime.utcnow().replace(tzinfo=pytz.utc)
            beijing_time = utc_now.astimezone(pytz.timezone("Asia/Shanghai"))
            current_date = beijing_time.date()

            if beijing_time.hour == 8 and beijing_time.minute == 0:
                # 检查今天是否已经执行过预测任务
                if last_prediction_run[0] != current_date:
                    print(f"[{beijing_time}] 正在执行每日预测任务")
                    self.daily_prediction()
                    last_prediction_run[0] = current_date  # 标记今天已执行
            elif beijing_time.hour == 16 and beijing_time.minute == 0:
                # 检查今天是否已经执行过收盘价获取任务
                if last_fetch_run[0] != current_date:
                    print(f"[{beijing_time}] 正在执行收盘价获取任务")
                    self.fetch_all_closing_prices()
                    last_fetch_run[0] = current_date  # 标记今天已执行

        schedule.every(1).minutes.do(run_task)

        self.logger.info(f"定时任务已设置：每天08:00执行多symbol预测")
        self.logger.info(f"支持的交易对: {SYMBOLS}")
        self.logger.info("收盘价获取任务已设置：每天16:00执行（所有交易对）")

        # 在后台线程中运行调度器
        self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.scheduler_thread.start()
        self.logger.info("定时任务调度器已启动")


# 全局调度器服务实例
scheduler_service = SchedulerService()
